{"version": 3, "sources": ["components/main-form/components/form-select.uvue"], "names": [], "mappings": "AAmBC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,KAAK,YAAW,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,cAAA,EAAA,kDAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IACnB,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,GAAG,CAAA;CACX,CAAA;AAEA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,YAAY;IAClB,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,IAAG,IAAK,GAAE,IAAK,QAAQ,CAAC,aAAa,CAAA;SAC3C;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,IAAG,IAAK,GAAG;YACvB,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,QAAQ;YACjB,aAAa,EAAE,EAAC,IAAK,YAAY,EAAE;YACnC,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,CAAA;gBACzB,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;oBACzB,IAAI,CAAC,kBAAkB,EAAC,CAAA;iBACzB;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,CAAA;YAEhC,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAC1C,IAAI,CAAC,OAAM,GAAI,SAAS,CAAC,SAAS,CAAC,SAAS,CAAA,IAAK,QAAO,CAAA;YACxD,IAAI,CAAC,WAAU,GAAI,SAAS,CAAC,SAAS,CAAC,aAAa,CAAA,IAAK,KAAI,CAAA;YAE7D,SAAQ;YACR,MAAM,YAAW,GAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAA,CAAA;YACjD,IAAI,YAAW,IAAK,IAAI,EAAE;gBACzB,IAAI,CAAC,aAAY,GAAI,EAAC,CAAA;gBACtB,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC7C,MAAM,SAAQ,GAAI,YAAY,CAAC,CAAC,CAAA,IAAK,aAAY,CAAA;oBACjD,MAAM,MAAM,EAAE,YAAW,GAAI;wBAC5B,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAA,IAAK,EAAE;wBACvC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAA;qBAC7B,CAAA;oBACA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAA,CAAA;iBAC/B;aACD;YAEA,SAAQ;YACR,IAAI,CAAC,kBAAkB,EAAC,CAAA;YAExB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,WAAU;QACV,kBAAkB,IAAI,IAAG;YACxB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC5B,YAAW;gBACX,MAAM,cAAa,GAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,YAAY,GAAG,OAAM,CAAE,EAAC;oBAC/E,OAAO,MAAM,CAAC,KAAI,KAAM,IAAI,CAAC,UAAS,CAAA;gBACvC,CAAC,CAAA,CAAA;gBACD,IAAI,cAAa,IAAK,IAAI,EAAE;oBAC3B,IAAI,CAAC,YAAW,GAAI,cAAc,CAAC,IAAG,CAAA;iBACvC;qBAAO;oBACN,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;iBACtB;aACD;iBAAO;gBACN,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;aACtB;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,IAAI,UAAS,GAAI,GAAG,CAAC,IAAG,CAAA;wBACxB,gBAAe;wBACf,IAAI,IAAI,CAAC,OAAM,IAAK,KAAK,EAAE;4BAC1B,UAAS,GAAI,QAAQ,CAAC,UAAS,IAAK,MAAM,CAAA,CAAA;yBAC3C;6BAAO,IAAI,IAAI,CAAC,OAAM,IAAK,OAAO,EAAE;4BACnC,UAAS,GAAI,UAAU,CAAC,UAAS,IAAK,MAAM,CAAA,CAAA;yBAC7C;6BAAO;4BACN,UAAS,GAAI,UAAS,IAAK,MAAK,CAAA;yBACjC;wBAEA,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;wBAC3B,IAAI,CAAC,kBAAkB,EAAC,CAAA;wBACxB,MAAM,MAAM,EAAE,eAAc,GAAI;4BAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,KAAK,EAAE,UAAS;yBACjB,CAAA;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;oBACnB,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAK,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC3C,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,QAAO;YACP,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;gBAC5B,OAAO,KAAI,CAAA;aACZ;YAEA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,CAAA;YAC5B,SAAQ;YACR,IAAI,CAAC,kBAAkB,EAAC,CAAA;YACxB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QAED,QAAO;QACP,YAAY,IAAI,IAAG;YAClB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAK,IAAK,CAAC,EAAE;gBACnC,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,MAAK;iBACX,CAAA,CAAA;gBACD,OAAK;aACN;YAEA,MAAM,QAAO,GAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,YAAY,GAAG,MAAK,CAAE,EAAC;gBACvE,OAAO,MAAM,CAAC,IAAG,CAAA;YAClB,CAAC,CAAA,CAAA;YAED,GAAG,CAAC,eAAe,CAAC;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,sBAAsB,EAAE,EAAC;oBACvC,MAAM,aAAY,GAAI,GAAG,CAAC,QAAO,CAAA;oBACjC,MAAM,cAAa,GAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAA,CAAA;oBAEvD,IAAI,aAAY,GAAI,cAAc,CAAC,KAAI,CAAA;oBACvC,gBAAe;oBACf,IAAI,IAAI,CAAC,OAAM,IAAK,KAAK,EAAE;wBAC1B,aAAY,GAAI,QAAQ,CAAC,aAAY,IAAK,MAAM,CAAA,CAAA;qBACjD;yBAAO,IAAI,IAAI,CAAC,OAAM,IAAK,OAAO,EAAE;wBACnC,aAAY,GAAI,UAAU,CAAC,aAAY,IAAK,MAAM,CAAA,CAAA;qBACnD;yBAAO;wBACN,aAAY,GAAI,aAAY,IAAK,MAAK,CAAA;qBACvC;oBAEA,MAAM,MAAM,EAAE,eAAc,GAAI;wBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,KAAK,EAAE,aAAY;qBACpB,CAAA;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;gBACnB,CAAC;gBACD,IAAI,EAAE,GAAG,EAAC;oBACT,OAAO,CAAC,GAAG,CAAC,MAAM,EAAA,0DAAA,CAAA,CAAA;gBACnB,CAAA;aACA,CAAA,CAAA;QACF,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WAxPA,GAAA,CAciB,yBAAA,EAAA,GAAA,CAAA;QAdA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IASO,GAAA,EAAA,CAAA,EAAA,CAAA;YATP,GAAA,CASO,MAAA,EAAA,GAAA,CAAA;gBATD,KAAK,EAAC,kBAAkB;gBAAE,OAAK,EAAE,IAAA,CAAA,YAAY;;gBAClD,GAAA,CAIO,MAAA,EAAA,GAAA,CAAA,EAJD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;oBAChC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;wBAFD,KAAK,EAAA,GAAA,CAAA,CAAC,aAAa,EAAS,GAAA,CAAA,EAAA,oBAAA,EAAA,IAAA,CAAA,YAAA,KAAA,EAAA,EAAA,CAAyC,CAAA,CAAA;4BACvE,IAAA,CAAA,WAAW,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;;gBAGhB,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;oBAChC,GAAA,CAAkC,MAAA,EAAA,GAAA,CAAA,EAA5B,KAAK,EAAC,aAAa,EAAA,CAAA,EAAC,GAAC,CAAA", "file": "components/main-form/components/form-select.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"select-container\" @click=\"showSelector\">\n\t\t\t\t<view class=\"select-text-wrapper\">\n\t\t\t\t\t<text class=\"select-text\" :class=\"{'select-placeholder': selectedText===''}\">\n\t\t\t\t\t\t{{ displayText }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"select-icon-wrapper\">\n\t\t\t\t\t<text class=\"select-icon\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\ttype SelectOption = {\n\t\ttext: string;\n\t\tvalue: any;\n\t}\n\n\texport default {\n\t\tname: \"FormSelect\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"string\",\n\t\t\t\tselectOptions: [] as SelectOption[],\n\t\t\t\tselectedText: \"\",\n\t\t\t\tplaceholder: \"请选择\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSelectedText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"string\"\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"请选择\"\n\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.selectOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: SelectOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\")\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.selectOptions.push(option)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 更新选中文本\n\t\t\t\tthis.updateSelectedText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新选中文本显示\n\t\t\tupdateSelectedText(): void {\n\t\t\t\tif (this.fieldValue != null) {\n\t\t\t\t\t// 查找对应的选项文本\n\t\t\t\t\tconst selectedOption = this.selectOptions.find((option: SelectOption): boolean => {\n\t\t\t\t\t\treturn option.value === this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\t\tthis.selectedText = selectedOption.text\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tlet save_value = res.data\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"int\") {\n\t\t\t\t\t\t\t\tsave_value = parseInt(save_value as string)\n\t\t\t\t\t\t\t} else if (that.varType == \"float\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(save_value as string)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tsave_value = save_value as string\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tthat.updateSelectedText()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 选择器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateSelectedText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshowSelector(): void {\n\t\t\t\tif (this.selectOptions.length == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无选项',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst itemList = this.selectOptions.map((option: SelectOption): string => {\n\t\t\t\t\treturn option.text\n\t\t\t\t})\n\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res: ShowActionSheetSuccess) => {\n\t\t\t\t\t\tconst selectedIndex = res.tapIndex\n\t\t\t\t\t\tconst selectedOption = this.selectOptions[selectedIndex]\n\n\t\t\t\t\t\tlet selectedValue = selectedOption.value\n\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\tif (this.varType == \"int\") {\n\t\t\t\t\t\t\tselectedValue = parseInt(selectedValue as string)\n\t\t\t\t\t\t} else if (this.varType == \"float\") {\n\t\t\t\t\t\t\tselectedValue = parseFloat(selectedValue as string)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tselectedValue = selectedValue as string\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: selectedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tconsole.log('选择取消')\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.select-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.select-text-wrapper {\n\t\tflex: 1;\n\t}\n\n\t.select-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.select-placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.select-icon-wrapper {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.select-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n</style>"]}