<template>
	<form-container :label="fieldName" :show-error="showError" :tip="tip" :error-message="errorMessage" :label-color="labelColor"
		:background-color="backgroundColor">
		<template #input-content>
			<view class="color-display-container" @click="openColorPicker">
				<view class="color-preview" :style="{ backgroundColor: displayColor }"></view>
				<text class="color-text">{{ displayColor }}</text>
			</view>
		</template>
	</form-container>

	<!-- 颜色选择器 -->
	<main-color-picker ref="colorPicker" @confirm="onColorConfirm" @cancel="onColorCancel"></main-color-picker>
</template>

<script lang="uts">
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'
	import MainColorPicker from '@/components/main-color-picker/main-color-picker.uvue'

	export default {
		name: "FormColor",
		emits: ['change'],
		components: {
			FormContainer,
			MainColorPicker
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: "",
				isSave: false,
				save_key: "",
				tip: "",
				varType: "hex",
				displayColor: "#000000",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					// 这避免了用户输入时的循环更新问题
					const newValue = obj.value as string
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateDisplayColor()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as string

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""
				this.varType = extalJson.getString("varType") ?? "hex"

				// 更新显示颜色
				this.updateDisplayColor()

				// 获取缓存
				this.getCache()
			},

			// 更新显示颜色
			updateDisplayColor(): void {
				if (this.fieldValue != null && this.fieldValue != "") {
					this.displayColor = this.fieldValue
				} else {
					// 根据varType设置默认颜色
					if (this.varType == "rgba") {
						this.displayColor = "rgba(0, 0, 0, 1)"
						this.fieldValue = "rgba(0, 0, 0, 1)"
					} else {
						this.displayColor = "#000000"
						this.fieldValue = "#000000"
					}
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data as string
							that.fieldValue = save_value
							that.updateDisplayColor()
							const result: FormChangeEvent = {
								index: this.index,
								value: save_value
							}
							this.change(result)
						}
					})
				}
			},

			setCache(): void {
				if (this.isSave) {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 颜色值验证
				if (this.fieldValue == null || this.fieldValue == "") {
					this.showError = true
					this.errorMessage = "请选择颜色"
					return false
				}

				// 根据varType验证颜色格式
				if (this.varType == "hex") {
					const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
					if (!hexPattern.test(this.fieldValue)) {
						this.showError = true
						this.errorMessage = "颜色格式不正确"
						return false
					}
				} else if (this.varType == "rgba") {
					const rgbaPattern = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
					if (!rgbaPattern.test(this.fieldValue)) {
						this.showError = true
						this.errorMessage = "颜色格式不正确"
						return false
					}
				}

				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as string
				// 更新显示颜色
				this.updateDisplayColor()
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 打开颜色选择器
			openColorPicker(): void {
				const colorPicker = this.$refs["colorPicker"] as ComponentPublicInstance
				if (colorPicker != null) {
					colorPicker.$callMethod("open")
				}
			},

			// 颜色选择确认
			onColorConfirm(colorData: UTSJSONObject): void {
				let selectedColor: string

				if (this.varType == "rgba") {
					// 使用rgba格式
					selectedColor = colorData.getString("color") ?? "rgba(0, 0, 0, 1)"
				} else {
					// 使用hex格式
					selectedColor = colorData.getString("hex") ?? "#000000"
				}

				const result: FormChangeEvent = {
					index: this.index,
					value: selectedColor
				}
				this.change(result)
			},

			// 颜色选择取消
			onColorCancel(): void {
				// 取消选择，不做任何操作
			}
		}
	}
</script>

<style>
	.color-display-container {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		min-height: 60rpx;
		padding: 10rpx;
		border-radius: 10rpx;
		background-color: rgba(255, 255, 255, 0.8);
	}

	.color-preview {
		width: 60rpx;
		height: 40rpx;
		border-radius: 8rpx;
		border: 1rpx solid #e5e5e5;
		margin-right: 20rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.color-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
		font-family: monospace;
	}
</style>