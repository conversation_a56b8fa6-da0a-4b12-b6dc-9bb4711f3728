{"version": 3, "sources": ["components/main-form/components/form-select.uvue", "components/main-form/components/form-input.uvue", "components/main-color-picker/main-color-picker.uvue", "App.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"select-container\" @click=\"showSelector\">\n\t\t\t\t<view class=\"select-text-wrapper\">\n\t\t\t\t\t<text class=\"select-text\" :class=\"{'select-placeholder': selectedText===''}\">\n\t\t\t\t\t\t{{ displayText }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"select-icon-wrapper\">\n\t\t\t\t\t<text class=\"select-icon\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\ttype SelectOption = {\n\t\ttext: string;\n\t\tvalue: any;\n\t}\n\n\texport default {\n\t\tname: \"FormSelect\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"string\",\n\t\t\t\tselectOptions: [] as SelectOption[],\n\t\t\t\tselectedText: \"\",\n\t\t\t\tplaceholder: \"请选择\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSelectedText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"string\"\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"请选择\"\n\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.selectOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: SelectOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\")\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.selectOptions.push(option)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 更新选中文本\n\t\t\t\tthis.updateSelectedText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新选中文本显示\n\t\t\tupdateSelectedText(): void {\n\t\t\t\tif (this.fieldValue != null) {\n\t\t\t\t\t// 查找对应的选项文本\n\t\t\t\t\tconst selectedOption = this.selectOptions.find((option: SelectOption): boolean => {\n\t\t\t\t\t\treturn option.value === this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\t\tthis.selectedText = selectedOption.text\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tlet save_value = res.data\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"int\") {\n\t\t\t\t\t\t\t\tsave_value = parseInt(save_value as string)\n\t\t\t\t\t\t\t} else if (that.varType == \"float\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(save_value as string)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tsave_value = save_value as string\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tthat.updateSelectedText()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 选择器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateSelectedText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshowSelector(): void {\n\t\t\t\tif (this.selectOptions.length == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无选项',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst itemList = this.selectOptions.map((option: SelectOption): string => {\n\t\t\t\t\treturn option.text\n\t\t\t\t})\n\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res: ShowActionSheetSuccess) => {\n\t\t\t\t\t\tconst selectedIndex = res.tapIndex\n\t\t\t\t\t\tconst selectedOption = this.selectOptions[selectedIndex]\n\n\t\t\t\t\t\tlet selectedValue = selectedOption.value\n\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\tif (this.varType == \"int\") {\n\t\t\t\t\t\t\tselectedValue = parseInt(selectedValue as string)\n\t\t\t\t\t\t} else if (this.varType == \"float\") {\n\t\t\t\t\t\t\tselectedValue = parseFloat(selectedValue as string)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tselectedValue = selectedValue as string\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: selectedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tconsole.log('选择取消')\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.select-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.select-text-wrapper {\n\t\tflex: 1;\n\t}\n\n\t.select-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.select-placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.select-icon-wrapper {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.select-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n</style>", null, null, null], "names": [], "mappings": ";;;;;;;;;;;;;+BA8GQ;+BAmBK;+BAoHR;+BAnNI;AAPH;;kBA0DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAfE,IAAQ,kBAAkB,EAAA;YAEzB,IAAM,WAAW,IAAI,KAAI;YACzB,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,kBAAkB;;QAEzB;uBACA,OAAM,IAAG;;;;;;;eAjFZ,IAciB,2BAAA,IAdA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBASO,GAAA;mBAAA;gBATP,IASO,QAAA,IATD,WAAM,oBAAoB,aAAO,KAAA,YAAY;oBAClD,IAIO,QAAA,IAJD,WAAM,wBAAqB;wBAChC,IAEO,QAAA,IAFD,WAAK,IAAA;4BAAC;4BAAsB,IAAA,yBAAA,KAAA,YAAA,KAAA;yBAAyC,QACvE,KAAA,WAAW,GAAA,CAAA;;oBAGhB,IAEO,QAAA,IAFD,WAAM,wBAAqB;wBAChC,IAAkC,QAAA,IAA5B,WAAM,gBAAc;;;;;;;;;;;;;;;;;;;;;aA6C3B;aACA,YAAoB,GAAG;aACvB;aACA;aACA;aACA;aACA,wBAAqB;aACrB;aACA;aACA;aACA;;;mBAVA,eAAW,IACX,gBAAY,IAAG,CAAA,EAAA,CAAK,GAAG,EACvB,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,aAAS,UACT,mBAAe,IAAM,iBACrB,kBAAc,IACd,iBAAa,OACb,eAAW,KAAK,EAChB,kBAAc;;aA0Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI;QAGhC,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,OAAM,GAAI,UAAU,SAAS,CAAC,cAAc;QACjD,IAAI,CAAC,WAAU,GAAI,UAAU,SAAS,CAAC,kBAAkB;QAGzD,IAAM,eAAe,UAAU,QAAQ,CAAC;QACxC,IAAI,gBAAgB,IAAI,EAAE;YACzB,IAAI,CAAC,aAAY,GAAI,KAAC;gBACtB;gBAAK,IAAI,YAAI,CAAC;gBAAd,MAAgB,IAAI,aAAa,MAAM;oBACtC,IAAM,YAAY,YAAY,CAAC,EAAC,CAAA,EAAA,CAAK;oBACrC,IAAM,sBACL,OAAM,UAAU,SAAS,CAAC,WAAW,IACrC,QAAO,UAAU,GAAG,CAAC;oBAEtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;oBANgB;;;;QAW1C,IAAI,CAAC,kBAAkB;QAGvB,IAAI,CAAC,QAAQ;IACd;aAGA;aAAA,6BAAsB,IAAG,CAAA;QACxB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAE5B,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAC,QAAQ,eAAe,OAAM,CAAG;gBAC/E,OAAO,OAAO,KAAI,KAAM,IAAI,CAAC,UAAS;YACvC;YACA,IAAI,kBAAkB,IAAI,EAAE;gBAC3B,IAAI,CAAC,YAAW,GAAI,eAAe,IAAG;mBAChC;gBACN,IAAI,CAAC,YAAW,GAAI;aACrB;eACM;YACN,IAAI,CAAC,YAAW,GAAI;;IAEtB;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAI,aAAa,IAAI,IAAG;gBAExB,IAAI,KAAK,OAAM,IAAK,OAAO;oBAC1B,aAAa,SAAS,WAAS,EAAA,CAAK,MAAM;uBACpC,IAAI,KAAK,OAAM,IAAK,SAAS;oBACnC,aAAa,WAAW,WAAS,EAAA,CAAK,MAAM;uBACtC;oBACN,aAAa,WAAS,EAAA,CAAK,MAAK;;gBAGjC,KAAK,UAAS,GAAI;gBAClB,KAAK,kBAAkB;gBACvB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;YACb;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAK,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAC3C,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAGZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI;QAE5B,IAAI,CAAC,kBAAkB;QAEvB,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAGA;aAAA,uBAAgB,IAAG,CAAA;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAK,IAAK,CAAC,EAAE;YACnC,+BACC,QAAO,QACP,OAAM;YAEP;;QAGD,IAAM,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAC,QAAQ,eAAe,MAAK,CAAG;YACvE,OAAO,OAAO,IAAG;QAClB;;QAEA,2CACC,WAAU,UACV,UAAS,IAAC,KAAK,uBAAyB;YACvC,IAAM,gBAAgB,IAAI,QAAO;YACjC,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,cAAa;YAEvD,IAAI,gBAAgB,eAAe,KAAI;YAEvC,IAAI,IAAI,CAAC,OAAM,IAAK,OAAO;gBAC1B,gBAAgB,SAAS,cAAY,EAAA,CAAK,MAAM;mBAC1C,IAAI,IAAI,CAAC,OAAM,IAAK,SAAS;gBACnC,gBAAgB,WAAW,cAAY,EAAA,CAAK,MAAM;mBAC5C;gBACN,gBAAgB,cAAY,EAAA,CAAK,MAAK;;YAGvC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;YAER,IAAI,CAAC,MAAM,CAAC;QACb;UACA,OAAM,MAAI;YACT,QAAQ,GAAG,CAAC,QAAM;QACnB;;IAEF;;mBA3NK;;;;;;;;;;;;;6FAWK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAsMZ"}