@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
import io.dcloud.uniapp.extapi.showActionSheet as uni_showActionSheet
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenComponentsMainFormComponentsFormSelect : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            if (obj != null) {
                val newValue = obj.value
                if (newValue !== this.fieldValue) {
                    this.fieldValue = newValue
                    this.updateSelectedText()
                }
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("view", _uM("class" to "select-container", "onClick" to _ctx.showSelector), _uA(
                    _cE("view", _uM("class" to "select-text-wrapper"), _uA(
                        _cE("text", _uM("class" to _nC(_uA(
                            "select-text",
                            _uM("select-placeholder" to (_ctx.selectedText === ""))
                        ))), _tD(_ctx.displayText), 3)
                    )),
                    _cE("view", _uM("class" to "select-icon-wrapper"), _uA(
                        _cE("text", _uM("class" to "select-icon"), "▼")
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: FormFieldData? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: Any? by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var varType: String by `$data`
    open var selectOptions: UTSArray<SelectOption> by `$data`
    open var selectedText: String by `$data`
    open var placeholder: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    open var displayText: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to null as Any?, "isSave" to false as Boolean, "save_key" to "" as String, "tip" to "" as String, "varType" to "string" as String, "selectOptions" to _uA<SelectOption>(), "selectedText" to "" as String, "placeholder" to "请选择" as String, "showError" to false as Boolean, "errorMessage" to "" as String, "displayText" to computed<String>(fun(): String {
            if (this.selectedText != "") {
                return this.selectedText
            } else {
                return this.placeholder
            }
        }
        ))
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value
        this.fieldName = fieldObj.name as String
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        if (extalJson != null) {
            this.tip = extalJson.getString("tip") ?: ""
            this.varType = extalJson.getString("varType") ?: "string"
            this.placeholder = extalJson.getString("placeholder") ?: "请选择"
            val optionsArray = extalJson.getArray("options")
            if (optionsArray != null) {
                this.selectOptions = _uA()
                run {
                    var i: Number = 0
                    while(i < optionsArray.length){
                        val optionObj = optionsArray[i] as UTSJSONObject
                        val option = SelectOption(text = optionObj.getString("text") ?: "", value = optionObj.get("value") ?: "")
                        this.selectOptions.push(option)
                        i++
                    }
                }
            }
        }
        this.updateSelectedText()
        this.getCache()
    }
    open var updateSelectedText = ::gen_updateSelectedText_fn
    open fun gen_updateSelectedText_fn(): Unit {
        if (this.fieldValue != null) {
            val selectedOption = this.selectOptions.find(fun(option: SelectOption): Boolean {
                return option.value === this.fieldValue
            })
            if (selectedOption != null) {
                this.selectedText = selectedOption.text
            } else {
                this.selectedText = ""
            }
        } else {
            this.selectedText = ""
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                var save_value = res.data
                if (that.varType == "int") {
                    save_value = parseInt(save_value as String)
                } else if (that.varType == "float") {
                    save_value = parseFloat(save_value as String)
                } else {
                    save_value = save_value as String
                }
                that.fieldValue = save_value
                that.updateSelectedText()
                val result = FormChangeEvent(index = this.index, value = save_value)
                this.change(result)
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && this.fieldValue != null) {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == null) {
            this.showError = true
            this.errorMessage = "请选择一个选项"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value
        this.updateSelectedText()
        this.setCache()
        this.`$emit`("change", event)
    }
    open var showSelector = ::gen_showSelector_fn
    open fun gen_showSelector_fn(): Unit {
        if (this.selectOptions.length == 0) {
            uni_showToast(ShowToastOptions(title = "暂无选项", icon = "none"))
            return
        }
        val itemList = this.selectOptions.map(fun(option: SelectOption): String {
            return option.text
        }
        )
        uni_showActionSheet(ShowActionSheetOptions(itemList = itemList, success = fun(res: ShowActionSheetSuccess){
            val selectedIndex = res.tapIndex
            val selectedOption = this.selectOptions[selectedIndex]
            var selectedValue = selectedOption.value
            if (this.varType == "int") {
                selectedValue = parseInt(selectedValue as String)
            } else if (this.varType == "float") {
                selectedValue = parseFloat(selectedValue as String)
            } else {
                selectedValue = selectedValue as String
            }
            val result = FormChangeEvent(index = this.index, value = selectedValue)
            this.change(result)
        }
        , fail = fun(_){
            console.log("选择取消", " at components/main-form/components/form-select.uvue:255")
        }
        ))
    }
    companion object {
        var name = "FormSelect"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("select-container" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "minHeight" to "60rpx", "paddingTop" to "10rpx", "paddingRight" to "20rpx", "paddingBottom" to "10rpx", "paddingLeft" to "20rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "backgroundColor" to "rgba(255,255,255,0.8)")), "select-text-wrapper" to _pS(_uM("flex" to 1)), "select-text" to _pS(_uM("fontSize" to "28rpx", "color" to "#333333")), "select-placeholder" to _pS(_uM("color" to "#999999")), "select-icon-wrapper" to _pS(_uM("width" to "40rpx", "height" to "40rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center")), "select-icon" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM("type" to "Object"), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
