{"version": 3, "sources": ["pages/index/index.uvue", "components/main-form/components/form-container.uvue"], "sourcesContent": ["<template>\r\n\t<scroll-view  class=\"content\">\r\n\t\t<button @click=\"openColorPicker\">选择颜色</button>\r\n\t\t<button @click=\"openFun\">对话框</button>\r\n\t\t<main-color-picker ref=\"colorPicker\" @cancel=\"onCancel\" @confirm=\"onConfirm\"></main-color-picker>\r\n\r\n\t\t\r\n\t\t<main-form \r\n\t\t\t\t:formData=\"formConfig\"\r\n\t\t\t\ttitle=\"用户信息表单\"\r\n\t\t\t\tkeyName=\"user_form\"\r\n\t\t\t\tref=\"mainForm\"\r\n\t\t\t/>\r\n\t\t<button @click=\"viewForm\">查看表单1</button>\r\n\t</scroll-view>\r\n</template>\r\n\r\n<script>\r\n\timport { FormFieldData } from '@/components/main-form/form_type.uts'\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t    formConfig: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"username\",\r\n\t\t\t\t\t\tname: \"用户名1\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 0,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入用户名\",\r\n\t\t\t\t\t\t\ttip:\"123\",\r\n\t\t\t\t\t\t\tinputmode: \"digit\" \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"password\",\r\n\t\t\t\t\t\tname: \"密码\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:false,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"密码请自己保管好\",\r\n\t\t\t\t\t\t\tinputmode: \"number\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"email\",\r\n\t\t\t\t\t\tname: \"邮箱地址\",\r\n\t\t\t\t\t\ttype: \"textarea\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"enable_feature\",\r\n\t\t\t\t\t    name: \"启用功能\",\r\n\t\t\t\t\t    type: \"switch\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: { \r\n\t\t\t\t\t        \"varType\": \"number\",\r\n\t\t\t\t\t        \"tip\": \"开启后将启用此功能\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"slider\",\r\n\t\t\t\t\t    name: \"slider测试\",\r\n\t\t\t\t\t    type: \"slider\",\r\n\t\t\t\t\t    value: 10,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 0,\r\n\t\t\t\t\t        \"max\": 100,\r\n\t\t\t\t\t\t\t\"step\":1\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"numberbox\",\r\n\t\t\t\t\t    name: \"数量选择\",\r\n\t\t\t\t\t    type: \"numberbox\",\r\n\t\t\t\t\t    value: 5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 1,\r\n\t\t\t\t\t        \"max\": 50,\r\n\t\t\t\t\t\t\t\"step\": 1,\r\n\t\t\t\t\t\t\t\"unit\": \"个\",\r\n\t\t\t\t\t\t\t\"tip\": \"请选择数量\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"themeColor\",\r\n\t\t\t\t\t    name: \"主题颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"hex\",\r\n\t\t\t\t\t        \"tip\": \"选择您喜欢的主题颜色\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"backgroundColor\",\r\n\t\t\t\t\t    name: \"背景颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"rgba(255, 0, 0, 0.8)\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"rgba\",\r\n\t\t\t\t\t        \"tip\": \"选择背景颜色，支持透明度\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"city\",\r\n\t\t\t\t\t    name: \"所在城市\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"string\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择城市\",\r\n\t\t\t\t\t        \"tip\": \"选择您所在的城市\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"北京\", \"value\": \"beijing\"},\r\n\t\t\t\t\t            {\"text\": \"上海\", \"value\": \"shanghai\"},\r\n\t\t\t\t\t            {\"text\": \"广州\", \"value\": \"guangzhou\"},\r\n\t\t\t\t\t            {\"text\": \"深圳\", \"value\": \"shenzhen\"},\r\n\t\t\t\t\t            {\"text\": \"杭州\", \"value\": \"hangzhou\"}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"level\",\r\n\t\t\t\t\t    name: \"用户等级\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"int\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择等级\",\r\n\t\t\t\t\t        \"tip\": \"选择您的用户等级\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"初级用户\", \"value\": 1},\r\n\t\t\t\t\t            {\"text\": \"中级用户\", \"value\": 2},\r\n\t\t\t\t\t            {\"text\": \"高级用户\", \"value\": 3},\r\n\t\t\t\t\t            {\"text\": \"VIP用户\", \"value\": 4}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\r\n\t\t\t\t] as FormFieldData[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tviewForm(){\r\n\t\t\t\t// this.formConfig[0].value=\"111\"\r\n\t\t\t\tconsole.log(this.formConfig)\r\n\t\t\t},\r\n\t\t\topenFun() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: \"onLoad 调用示例,请手动取消\",\r\n\t\t\t\t\teditable: true,\r\n\t\t\t\t\tcontent: \"Hello World\",\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击确定')\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\topenColorPicker() {\r\n\t\t\t\t// 使用$callMethod方式调用组件方法\r\n\t\t\t\t(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t},\r\n\r\n\t\t\tonCancel() {\r\n\t\t\t\tconsole.log('用户取消选择')\r\n\t\t\t},\r\n\r\n\t\t\tonConfirm(result : UTSJSONObject) {\r\n\t\t\t\tconsole.log(result)\r\n\t\t\t\tconsole.log('选择的颜色:', result['color'])\r\n\t\t\t\tconsole.log('RGBA值:', result['rgba'])\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.content {\r\n\t\theight: 100%;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;;AAmBM;;;;;;;;eAlBL,IAac,eAAA,IAbA,WAAM,YAAS;YAC5B,IAA8C,UAAA,IAArC,aAAO,KAAA,eAAe,GAAE,QAAI,CAAA,EAAA;gBAAA;aAAA;YACrC,IAAqC,UAAA,IAA5B,aAAO,KAAA,OAAO,GAAE,OAAG,CAAA,EAAA;gBAAA;aAAA;YAC5B,IAAiG,8BAAA,IAA9E,SAAI,eAAe,cAAQ,KAAA,QAAQ,EAAG,eAAS,KAAA,SAAS;;;;YAG3E,IAKG,sBAAA,IAJA,cAAU,KAAA,UAAU,EACrB,WAAM,UACN,aAAQ,aACR,SAAI;;;YAEN,IAAwC,UAAA,IAA/B,aAAO,KAAA,QAAQ,GAAE,SAAK,CAAA,EAAA;gBAAA;aAAA;;;aAS1B;;;mBAAA,gBAAY,iCAEb,MAAK,YACL,OAAM,QACN,OAAM,SACN,QAAO,IACP,SAAO,IAAI,EACX,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;YACJ,IAAA,YAAW;SACZ,iBAGA,MAAK,YACL,OAAM,MACN,OAAM,SACN,QAAO,IACP,SAAO,KAAK,EACZ,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;YACJ,IAAA,YAAW;SACZ,iBAGA,MAAK,SACL,OAAM,QACN,OAAM,YACN,QAAO,IACP,SAAO,IAAI,EACX,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;SACL,iBAIG,MAAK,kBACL,OAAM,QACN,OAAM,UACN,QAAO,CAAC,EACR,SAAQ,KAAK,EACb,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,UACL,OAAM,YACN,OAAM,UACN,QAAO,EAAE,EACT,SAAQ,IAAI,EACZ,QAAO;YACH,kBAAO,CAAC;YACR,kBAAO,GAAG;YAChB,mBAAO,CAAA;SACL,iBAGA,MAAK,aACL,OAAM,QACN,OAAM,aACN,QAAO,CAAC,EACR,SAAQ,IAAI,EACZ,QAAO;YACH,kBAAO,CAAC;YACR,kBAAO,EAAE;YACf,mBAAQ,CAAC;YACT,WAAQ;YACR,UAAO;SACL,iBAGA,MAAK,cACL,OAAM,QACN,OAAM,SACN,QAAO,IACP,SAAQ,KAAK,EACb,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,mBACL,OAAM,QACN,OAAM,SACN,QAAO,wBACP,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,QACL,OAAM,QACN,OAAM,UACN,QAAO,IACP,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,kBAAe;YACf,UAAO;YACP,cAAW;gBACP;oBAAC,WAAQ;oBAAM,YAAS;iBAAU;gBAClC;oBAAC,WAAQ;oBAAM,YAAS;iBAAW;gBACnC;oBAAC,WAAQ;oBAAM,YAAS;iBAAY;gBACpC;oBAAC,WAAQ;oBAAM,YAAS;iBAAW;gBACnC;oBAAC,WAAQ;oBAAM,YAAS;iBAAU;aACtC;SACJ,iBAGA,MAAK,SACL,OAAM,QACN,OAAM,UACN,QAAO,CAAC,EACR,SAAQ,KAAK,EACb,QAAO;YACH,cAAW;YACX,kBAAe;YACf,UAAO;YACP,cAAW;gBACP;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAS,oBAAS,CAAC;iBAAA;aAChC;SACJ;;aAMN;aAAA,kBAAQ;QAEP,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,EAAA;IAC5B;aACA;aAAA,iBAAO;QACN,+BACC,QAAO,qBACP,WAAU,IAAI,EACd,UAAS,eACT,UAAS,IAAU,GAAG,EAAA;YACrB,IAAI,IAAI,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC,UAAQ;mBACd,IAAI,IAAI,MAAM,EAAE;gBACtB,QAAQ,GAAG,CAAC,UAAQ;;QAEtB;;IAEF;aACA;aAAA,yBAAe;QAEd,CAAC,IAAI,CAAC,OAAK,CAAC,cAAa,CAAA,EAAA,CAAK,uBAAuB,EAAE,aAAW,CAAC;IACpE;aAEA;aAAA,kBAAQ;QACP,QAAQ,GAAG,CAAC,UAAQ;IACrB;aAEA;aAAA,iBAAU,QAAS,aAAa,EAAA;QAC/B,QAAQ,GAAG,CAAC,QAAM;QAClB,QAAQ,GAAG,CAAC,UAAU,MAAM,CAAC,QAAQ,EAAA;QACrC,QAAQ,GAAG,CAAC,UAAU,MAAM,CAAC,OAAO,EAAA;IACrC;;;;;;;;;;;;;;;;;;;;AAEF"}