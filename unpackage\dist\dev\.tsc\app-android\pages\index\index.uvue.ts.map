{"version": 3, "file": "pages/index/index.uvue", "names": [], "sources": ["pages/index/index.uvue"], "sourcesContent": ["<template>\r\n\t<scroll-view  class=\"content\">\r\n\t\t<button @click=\"openColorPicker\">选择颜色</button>\r\n\t\t<button @click=\"openFun\">对话框</button>\r\n\t\t<main-color-picker ref=\"colorPicker\" @cancel=\"onCancel\" @confirm=\"onConfirm\"></main-color-picker>\r\n\r\n\t\t\r\n\t\t<main-form \r\n\t\t\t\t:formData=\"formConfig\"\r\n\t\t\t\ttitle=\"用户信息表单\"\r\n\t\t\t\tkeyName=\"user_form\"\r\n\t\t\t\tref=\"mainForm\"\r\n\t\t\t/>\r\n\t\t<button @click=\"viewForm\">查看表单1</button>\r\n\t</scroll-view>\r\n</template>\r\n\r\n<script>\r\n\timport { FormFieldData } from '@/components/main-form/form_type.uts'\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t    formConfig: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"username\",\r\n\t\t\t\t\t\tname: \"用户名1\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 0,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入用户名\",\r\n\t\t\t\t\t\t\ttip:\"123\",\r\n\t\t\t\t\t\t\tinputmode: \"digit\" \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"password\",\r\n\t\t\t\t\t\tname: \"密码\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:false,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"密码请自己保管好\",\r\n\t\t\t\t\t\t\tinputmode: \"number\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"email\",\r\n\t\t\t\t\t\tname: \"邮箱地址\",\r\n\t\t\t\t\t\ttype: \"textarea\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"enable_feature\",\r\n\t\t\t\t\t    name: \"启用功能\",\r\n\t\t\t\t\t    type: \"switch\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: { \r\n\t\t\t\t\t        \"varType\": \"number\",\r\n\t\t\t\t\t        \"tip\": \"开启后将启用此功能\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"slider\",\r\n\t\t\t\t\t    name: \"slider测试\",\r\n\t\t\t\t\t    type: \"slider\",\r\n\t\t\t\t\t    value: 10,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 0,\r\n\t\t\t\t\t        \"max\": 100,\r\n\t\t\t\t\t\t\t\"step\":1\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"numberbox\",\r\n\t\t\t\t\t    name: \"数量选择\",\r\n\t\t\t\t\t    type: \"numberbox\",\r\n\t\t\t\t\t    value: 5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 1,\r\n\t\t\t\t\t        \"max\": 50,\r\n\t\t\t\t\t\t\t\"step\": 1,\r\n\t\t\t\t\t\t\t\"unit\": \"个\",\r\n\t\t\t\t\t\t\t\"tip\": \"请选择数量\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"themeColor\",\r\n\t\t\t\t\t    name: \"主题颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"hex\",\r\n\t\t\t\t\t        \"tip\": \"选择您喜欢的主题颜色\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"backgroundColor\",\r\n\t\t\t\t\t    name: \"背景颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"rgba(255, 0, 0, 0.8)\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"rgba\",\r\n\t\t\t\t\t        \"tip\": \"选择背景颜色，支持透明度\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"city\",\r\n\t\t\t\t\t    name: \"所在城市\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"string\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择城市\",\r\n\t\t\t\t\t        \"tip\": \"选择您所在的城市\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"北京\", \"value\": \"beijing\"},\r\n\t\t\t\t\t            {\"text\": \"上海\", \"value\": \"shanghai\"},\r\n\t\t\t\t\t            {\"text\": \"广州\", \"value\": \"guangzhou\"},\r\n\t\t\t\t\t            {\"text\": \"深圳\", \"value\": \"shenzhen\"},\r\n\t\t\t\t\t            {\"text\": \"杭州\", \"value\": \"hangzhou\"}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"level\",\r\n\t\t\t\t\t    name: \"用户等级\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"int\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择等级\",\r\n\t\t\t\t\t        \"tip\": \"选择您的用户等级\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"初级用户\", \"value\": 1},\r\n\t\t\t\t\t            {\"text\": \"中级用户\", \"value\": 2},\r\n\t\t\t\t\t            {\"text\": \"高级用户\", \"value\": 3},\r\n\t\t\t\t\t            {\"text\": \"VIP用户\", \"value\": 4}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"score\",\r\n\t\t\t\t\t    name: \"评分\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 4.5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"float\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择评分\",\r\n\t\t\t\t\t        \"tip\": \"选择您的评分\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"1.0分\", \"value\": 1.0},\r\n\t\t\t\t\t            {\"text\": \"2.5分\", \"value\": 2.5},\r\n\t\t\t\t\t            {\"text\": \"3.0分\", \"value\": 3.0},\r\n\t\t\t\t\t            {\"text\": \"4.5分\", \"value\": 4.5},\r\n\t\t\t\t\t            {\"text\": \"5.0分\", \"value\": 5.0}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\r\n\t\t\t\t] as FormFieldData[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tviewForm(){\r\n\t\t\t\t// this.formConfig[0].value=\"111\"\r\n\t\t\t\tconsole.log(this.formConfig)\r\n\t\t\t},\r\n\t\t\topenFun() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: \"onLoad 调用示例,请手动取消\",\r\n\t\t\t\t\teditable: true,\r\n\t\t\t\t\tcontent: \"Hello World\",\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击确定')\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\topenColorPicker() {\r\n\t\t\t\t// 使用$callMethod方式调用组件方法\r\n\t\t\t\t(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t},\r\n\r\n\t\t\tonCancel() {\r\n\t\t\t\tconsole.log('用户取消选择')\r\n\t\t\t},\r\n\r\n\t\t\tonConfirm(result : UTSJSONObject) {\r\n\t\t\t\tconsole.log(result)\r\n\t\t\t\tconsole.log('选择的颜色:', result['color'])\r\n\t\t\t\tconsole.log('RGBA值:', result['rgba'])\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.content {\r\n\t\theight: 100%;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n</style>"], "mappings": ";CAkBC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACd;MACC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;KACD,CAAC;KACD;MACC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;KACD,CAAC;KACD;MACC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACpB,CAAC,CAAC,CAAC,CAAC,CAAC;MACN;;KAED,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACrB;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;aACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACL;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;aACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;OACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;OACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACX;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACxB;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;iBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtC;SACJ;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;aAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;iBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;aAChC;SACJ;KACJ,CAAC;KACD;SACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;aACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;iBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACjC;SACJ;KACJ;IACD,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC5B,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;OACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;KACD;IACD,CAAC;GACF,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC1E,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrB,CAAC;;GAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACrC;EACD;CACD;;;;;;;;;SAxNA,IAac,qBAbA,KAAK,EAAC,SAAS;IAC5B,IAA8C,gBAArC,OAAK,EAAE,oBAAe,KAAE,MAAI;IACrC,IAAqC,gBAA5B,OAAK,EAAE,YAAO,KAAE,KAAG;IAC5B,IAAiG;MAA9E,GAAG,EAAC,aAAa;MAAE,QAAM,EAAE,aAAQ;MAAG,SAAO,EAAE,cAAS;;IAG3E,IAKG;MAJA,QAAQ,EAAE,eAAU;MACrB,KAAK,EAAC,QAAQ;MACd,OAAO,EAAC,WAAW;MACnB,GAAG,EAAC,UAAU;;IAEhB,IAAwC,gBAA/B,OAAK,EAAE,aAAQ,KAAE,OAAK"}