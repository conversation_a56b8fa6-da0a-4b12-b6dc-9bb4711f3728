import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts';
import FormContainer from './form-container.uvue';
type SelectOption = {
    __$originalPosition?: UTSSourceMapPosition<"SelectOption", "components/main-form/components/form-select.uvue", 23, 7>;
    text: string;
    value: any;
};
const __sfc__ = defineComponent({
    name: "FormSelect",
    emits: ['change'],
    components: {
        FormContainer
    },
    props: {
        data: {
            type: Object as PropType<FormFieldData>
        },
        index: {
            type: Number,
            default: 0
        },
        keyName: {
            type: String,
            default: ""
        },
        labelColor: {
            type: String,
            default: "#000"
        },
        backgroundColor: {
            type: String,
            default: "#f1f4f9"
        }
    },
    data() {
        return {
            fieldName: "",
            fieldValue: null as any | null,
            isSave: false as boolean,
            save_key: "" as string,
            tip: "" as string,
            varType: "string" as string,
            selectOptions: [] as SelectOption[],
            selectedText: "" as string,
            placeholder: "请选择1" as string,
            showError: false as boolean,
            errorMessage: "" as string
        };
    },
    computed: {
        displayText(): string {
            if (this.selectedText != "") {
                return this.selectedText;
            }
            else {
                return this.placeholder;
            }
        }
    },
    watch: {
        data: {
            handler(obj: FormFieldData) {
                // 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
                const newValue = obj.value;
                if (newValue !== this.fieldValue) {
                    this.fieldValue = newValue;
                    this.updateSelectedText();
                }
            },
            deep: true
        }
    },
    created(): void {
        // 初始化时调用一次即可
        const fieldObj = this.$props["data"] as FormFieldData;
        this.initFieldData(fieldObj);
    },
    methods: {
        // 初始化字段数据（仅在首次加载时调用）
        initFieldData(fieldObj: FormFieldData): void {
            const fieldKey = fieldObj.key;
            const fieldValue = fieldObj.value;
            // 设置基本信息
            this.fieldName = fieldObj.name as string;
            this.fieldValue = fieldValue;
            this.isSave = fieldObj.isSave ?? false;
            this.save_key = this.keyName + "_" + fieldKey;
            // 解析配置信息
            const extalJson = fieldObj.extra as UTSJSONObject;
            this.tip = extalJson.getString("tip") ?? "";
            const configVarType = extalJson.getString("varType") ?? "string";
            // 校验 varType 的有效性
            this.varType = this.validateVarType(configVarType);
            this.placeholder = extalJson.getString("placeholder") ?? "请选择";
            // 解析选项数据
            const optionsArray = extalJson.getArray("options");
            if (optionsArray != null) {
                this.selectOptions = [];
                for (let i = 0; i < optionsArray.length; i++) {
                    const optionObj = optionsArray[i] as UTSJSONObject;
                    const option: SelectOption = {
                        text: optionObj.getString("text") ?? "",
                        value: optionObj.get("value") ?? ""
                    };
                    this.selectOptions.push(option);
                }
            }
            // 更新选中文本
            this.updateSelectedText();
            // 获取缓存
            this.getCache();
        },
        // 校验 varType 的有效性
        validateVarType(varType: string): string {
            const validTypes = ["string", "int", "float"];
            if (validTypes.includes(varType)) {
                return varType;
            }
            else {
                console.warn(`Invalid varType: ${varType}, using default 'string'`, " at components/main-form/components/form-select.uvue:143");
                return "string";
            }
        },
        // 更新选中文本显示
        updateSelectedText(): void {
            if (this.fieldValue != null) {
                // 查找对应的选项文本
                const selectedOption = this.selectOptions.find((option: SelectOption): boolean => {
                    return option.value === this.fieldValue;
                });
                if (selectedOption != null) {
                    this.selectedText = selectedOption.text;
                }
                else {
                    this.selectedText = "";
                }
            }
            else {
                this.selectedText = "";
            }
        },
        getCache(): void {
            if (this.isSave) {
                const that = this;
                uni.getStorage({
                    key: this.save_key,
                    success: (res: GetStorageSuccess) => {
                        const cacheData = res.data as string;
                        let save_value: any;
                        // 根据varType转换类型
                        if (that.varType == "int") {
                            save_value = parseInt(cacheData);
                            // 验证转换结果
                            if (isNaN(save_value)) {
                                return; // 转换失败，不使用缓存
                            }
                        }
                        else if (that.varType == "float") {
                            save_value = parseFloat(cacheData);
                            // 验证转换结果
                            if (isNaN(save_value)) {
                                return; // 转换失败，不使用缓存
                            }
                        }
                        else {
                            // varType == "string"
                            save_value = cacheData;
                        }
                        that.fieldValue = save_value;
                        that.updateSelectedText();
                        const result: FormChangeEvent = {
                            index: this.index,
                            value: save_value
                        };
                        this.change(result);
                    }
                });
            }
        },
        setCache(): void {
            if (this.isSave && this.fieldValue != null) {
                // 统一以字符串形式存储
                const cacheValue = this.fieldValue.toString();
                uni.setStorage({
                    key: this.save_key,
                    data: cacheValue
                });
            }
        },
        validate(): boolean {
            // 选择器验证
            if (this.fieldValue == null) {
                this.showError = true;
                this.errorMessage = "请选择一个选项";
                return false;
            }
            this.showError = false;
            this.errorMessage = "";
            return true;
        },
        change(event: FormChangeEvent): void {
            console.log(event, " at components/main-form/components/form-select.uvue:230");
            // 更新字段值
            this.fieldValue = event.value;
            // 更新显示文本
            this.updateSelectedText();
            // 保存缓存
            this.setCache();
            // 触发父组件事件
            this.$emit('change', event);
        },
        // 显示选择器
        showSelector(): void {
            if (this.selectOptions.length == 0) {
                uni.showToast({
                    title: '暂无选项',
                    icon: 'none'
                });
                return;
            }
            const itemList = this.selectOptions.map((option: SelectOption): string => {
                return option.text;
            });
            uni.showActionSheet({
                itemList: itemList,
                success: (res: ShowActionSheetSuccess) => {
                    const selectedIndex = res.tapIndex;
                    const selectedOption = this.selectOptions[selectedIndex] as SelectOption;
                    let selectedValue = selectedOption.value;
                    // 根据varType转换类型
                    if (this.varType == "int") {
                        selectedValue = parseInt(selectedValue as string);
                    }
                    else if (this.varType == "float") {
                        selectedValue = parseFloat(selectedValue as string);
                    }
                    else {
                        // varType == "string"
                        selectedValue = selectedValue as string;
                    }
                    const result: FormChangeEvent = {
                        index: this.index,
                        value: selectedValue
                    };
                    this.change(result);
                },
                fail: () => {
                    console.log('选择取消', " at components/main-form/components/form-select.uvue:281");
                }
            });
        }
    }
});
export default __sfc__;
function GenComponentsMainFormComponentsFormSelectRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    const _component_form_container = resolveComponent("form-container");
    return _cV(_component_form_container, _uM({
        label: _ctx.fieldName,
        "show-error": _ctx.showError,
        tip: _ctx.tip,
        "error-message": _ctx.errorMessage,
        "label-color": _ctx.labelColor,
        "background-color": _ctx.backgroundColor
    }), _uM({
        "input-content": withSlotCtx((): any[] => [
            _cE("view", _uM({
                class: "select-container",
                onClick: _ctx.showSelector
            }), [
                _cE("view", _uM({ class: "select-text-wrapper" }), [
                    _cE("text", _uM({
                        class: _nC(["select-text", _uM({ 'select-placeholder': _ctx.selectedText === '' })])
                    }), _tD(_ctx.displayText), 3 /* TEXT, CLASS */)
                ]),
                _cE("view", _uM({ class: "select-icon-wrapper" }), [
                    _cE("text", _uM({ class: "select-icon" }), "▼")
                ])
            ], 8 /* PROPS */, ["onClick"])
        ]),
        _: 1 /* STABLE */
    }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]);
}
const GenComponentsMainFormComponentsFormSelectStyles = [_uM([["select-container", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["minHeight", "60rpx"], ["paddingTop", "10rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "20rpx"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["backgroundColor", "rgba(255,255,255,0.8)"]]))], ["select-text-wrapper", _pS(_uM([["flex", 1]]))], ["select-text", _pS(_uM([["fontSize", "28rpx"], ["color", "#333333"]]))], ["select-placeholder", _pS(_uM([["color", "#999999"]]))], ["select-icon-wrapper", _pS(_uM([["width", "40rpx"], ["height", "40rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"]]))], ["select-icon", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"]]))]])];
//# sourceMappingURL=form-select.uvue.map