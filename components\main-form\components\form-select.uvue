<template>
	<form-container :label="fieldName" :show-error="showError" :tip="tip" :error-message="errorMessage" :label-color="labelColor"
		:background-color="backgroundColor">
		<template #input-content>
			<view class="select-container" @click="showSelector">
				<view class="select-text-wrapper">
					<text class="select-text" :class="{'select-placeholder': !selectedText}">
						{{ selectedText || placeholder }}
					</text>
				</view>
				<view class="select-icon-wrapper">
					<text class="select-icon">▼</text>
				</view>
			</view>
		</template>
	</form-container>
</template>

<script lang="uts">
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'

	type SelectOption = {
		text: string;
		value: any;
	}

	export default {
		name: "FormSelect",
		emits: ['change'],
		components: {
			FormContainer
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: null as any,
				isSave: false,
				save_key: "",
				tip: "",
				varType: "string",
				selectOptions: [] as SelectOption[],
				selectedText: "",
				placeholder: "请选择",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					const newValue = obj.value
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateSelectedText()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""
				this.varType = extalJson.getString("varType") ?? "string"
				this.placeholder = extalJson.getString("placeholder") ?? "请选择"

				// 解析选项数据
				const optionsArray = extalJson.getArray("options")
				if (optionsArray != null) {
					this.selectOptions = []
					for (let i = 0; i < optionsArray.length; i++) {
						const optionObj = optionsArray[i] as UTSJSONObject
						const option: SelectOption = {
							text: optionObj.getString("text") ?? "",
							value: optionObj.get("value")
						}
						this.selectOptions.push(option)
					}
				}

				// 更新选中文本
				this.updateSelectedText()

				// 获取缓存
				this.getCache()
			},

			// 更新选中文本显示
			updateSelectedText(): void {
				if (this.fieldValue != null) {
					// 查找对应的选项文本
					const selectedOption = this.selectOptions.find((option: SelectOption): boolean => {
						return option.value === this.fieldValue
					})
					if (selectedOption != null) {
						this.selectedText = selectedOption.text
					} else {
						this.selectedText = ""
					}
				} else {
					this.selectedText = ""
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							let save_value = res.data
							// 根据varType转换类型
							if (that.varType == "int") {
								save_value = parseInt(save_value as string)
							} else if (that.varType == "float") {
								save_value = parseFloat(save_value as string)
							} else {
								save_value = save_value as string
							}

							that.fieldValue = save_value
							that.updateSelectedText()
							const result: FormChangeEvent = {
								index: this.index,
								value: save_value
							}
							this.change(result)
						}
					})
				}
			},

			setCache(): void {
				if (this.isSave && this.fieldValue != null) {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 选择器验证
				if (this.fieldValue == null) {
					this.showError = true
					this.errorMessage = "请选择一个选项"
					return false
				}

				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value
				// 更新显示文本
				this.updateSelectedText()
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 显示选择器
			showSelector(): void {
				if (this.selectOptions.length == 0) {
					uni.showToast({
						title: '暂无选项',
						icon: 'none'
					})
					return
				}

				const itemList = this.selectOptions.map((option: SelectOption): string => {
					return option.text
				})

				uni.showActionSheet({
					itemList: itemList,
					success: (res: ShowActionSheetSuccess) => {
						const selectedIndex = res.tapIndex
						const selectedOption = this.selectOptions[selectedIndex]

						let selectedValue = selectedOption.value
						// 根据varType转换类型
						if (this.varType == "int") {
							selectedValue = parseInt(selectedValue as string)
						} else if (this.varType == "float") {
							selectedValue = parseFloat(selectedValue as string)
						} else {
							selectedValue = selectedValue as string
						}

						const result: FormChangeEvent = {
							index: this.index,
							value: selectedValue
						}
						this.change(result)
					},
					fail: () => {
						console.log('选择取消')
					}
				})
			}
		}
	}
</script>

<style>
	.select-container {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		min-height: 60rpx;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
		background-color: rgba(255, 255, 255, 0.8);
	}

	.select-text-wrapper {
		flex: 1;
	}

	.select-text {
		font-size: 28rpx;
		color: #333333;
	}

	.select-placeholder {
		color: #999999;
	}

	.select-icon-wrapper {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.select-icon {
		font-size: 24rpx;
		color: #666666;
	}
</style>